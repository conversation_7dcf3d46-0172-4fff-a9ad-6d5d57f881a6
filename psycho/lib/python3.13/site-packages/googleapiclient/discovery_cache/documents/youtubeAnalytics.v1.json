{"name": "youtubeAnalytics", "batchPath": "batch", "mtlsRootUrl": "https://youtubeanalytics.mtls.googleapis.com/", "fullyEncodeReservedExpansion": true, "title": "YouTube Analytics API", "ownerName": "Google", "resources": {}, "parameters": {"quotaUser": {"type": "string", "location": "query", "description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters."}, "prettyPrint": {"type": "boolean", "default": "true", "location": "query", "description": "Returns response with indentations and line breaks."}, "uploadType": {"type": "string", "location": "query", "description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\")."}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "type": "string", "location": "query"}, "callback": {"description": "JSONP", "type": "string", "location": "query"}, "oauth_token": {"type": "string", "location": "query", "description": "OAuth 2.0 token for the current user."}, "$.xgafv": {"description": "V1 error format.", "type": "string", "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "enum": ["1", "2"]}, "alt": {"location": "query", "description": "Data format for response.", "default": "json", "enum": ["json", "media", "proto"], "type": "string", "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"]}, "key": {"type": "string", "location": "query", "description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token."}, "access_token": {"location": "query", "description": "OAuth access token.", "type": "string"}, "upload_protocol": {"type": "string", "location": "query", "description": "Upload protocol for media (e.g. \"raw\", \"multipart\")."}}, "version": "v1", "baseUrl": "https://youtubeanalytics.googleapis.com/", "servicePath": "", "description": "Retrieves your YouTube Analytics data.", "kind": "discovery#restDescription", "basePath": "", "revision": "20200215", "documentationLink": "https://developers.google.com/youtube/analytics", "id": "youtubeAnalytics:v1", "discoveryVersion": "v1", "version_module": true, "schemas": {}, "protocol": "rest", "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "canonicalName": "YouTube Analytics", "rootUrl": "https://youtubeanalytics.googleapis.com/", "ownerDomain": "google.com"}