"""
Automated & Balanced Question Generation Service with FAISS (POC Version)
A sophisticated Python service using FastAPI for psychometric question generation and validation.
"""

import os
import json
import uuid
import logging
import pickle
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

import numpy as np
import faiss
import google.generativeai as genai
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
from PyPDF2 import PdfReader
from langchain_text_splitters import RecursiveCharacterTextSplitter

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Psychometric Question Generation Service",
    description="Automated & Balanced Question Generation Service with FAISS-powered validation",
    version="1.0.0"
)

# Configuration Maps
THEORY_CONSTRUCT_MAP = {
    "Big Five Personality": ["Agreeableness", "Conscientiousness", "Extraversion", "Openness", "Neuroticism"],
    "Holland's RIASEC": ["Realistic", "Investigative", "Artistic", "Social", "Enterprising", "Conventional"]
}

GRADE_AGE_MAP = {
    "7": "12-13", "8": "13-14", "9": "14-15",
    "10": "15-16", "11": "16-17", "12": "17-18"
}

# Global variables for FAISS and embeddings
faiss_index = None
text_chunks = []
chunk_metadata = []

# Pydantic Models
class ScoringKey(BaseModel):
    trait: str
    weight: int

class Option(BaseModel):
    option_id: str
    option_text: str
    scoring_key: ScoringKey

class QuestionMetadata(BaseModel):
    theory: str
    construct_name: str
    grade: int
    target_age_group: str
    batch_id: Optional[str] = None
    status: str = "UNVALIDATED"

class Question(BaseModel):
    question_id: str
    question_text: str
    options: List[Option]
    metadata: QuestionMetadata

class GenerationRequest(BaseModel):
    generation_count: int = Field(..., ge=1, le=100)
    theory: str
    grade: int = Field(..., ge=7, le=12)

class ValidationRequest(BaseModel):
    question: Question

class BatchValidationRequest(BaseModel):
    questions: List[Question]

class DetailedAnalysis(BaseModel):
    theoretical_alignment: str
    age_appropriateness: str
    scoring_validity: str
    bias_assessment: str

class ValidationResult(BaseModel):
    verdict: str
    confidence_score: float
    justification: str
    detailed_analysis: DetailedAnalysis

class QuestionValidationResponse(BaseModel):
    question_id: str
    validation_result: ValidationResult
    retrieval_info: Dict[str, Any]

class GenerationResponse(BaseModel):
    message: str
    batch_id: str
    total_questions_created: int
    construct_split: Dict[str, int]
    questions: List[Question]

class BatchValidationResponse(BaseModel):
    batch_validation_results: List[QuestionValidationResponse]
    summary: Dict[str, Any]

# PDF Processing Functions
def extract_text_from_pdf(pdf_path: str) -> str:
    """Extract text from a PDF file"""
    try:
        reader = PdfReader(pdf_path)
        text = ""
        for page in reader.pages:
            text += page.extract_text() + "\n"
        return text
    except Exception as e:
        logger.error(f"Failed to extract text from {pdf_path}: {e}")
        return ""

def convert_text_to_markdown(text: str, source_file: str) -> str:
    """Convert extracted text to markdown format"""
    try:
        # Clean up the text
        lines = text.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Convert to markdown format
            # Detect headings (lines that are all caps or start with numbers)
            if line.isupper() and len(line) > 5:
                cleaned_lines.append(f"## {line.title()}")
            elif line.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.')):
                cleaned_lines.append(f"### {line}")
            elif line.startswith(('Chapter', 'CHAPTER')):
                cleaned_lines.append(f"# {line}")
            else:
                cleaned_lines.append(line)

        # Join with proper spacing
        markdown_text = '\n\n'.join(cleaned_lines)

        # Add source header
        final_markdown = f"# Source: {source_file}\n\n{markdown_text}"

        return final_markdown

    except Exception as e:
        logger.error(f"Failed to convert text to markdown: {e}")
        return text

def process_pdfs_to_chunks(pdf_folder: str = "pdf") -> List[Dict[str, Any]]:
    """Process all PDFs in the folder and create text chunks"""
    chunks = []
    
    # Initialize text splitter with specified parameters
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=2000,
        chunk_overlap=700,
        length_function=len,
        separators=["\n\n", "\n", " ", ""]
    )
    
    pdf_folder_path = Path(pdf_folder)
    if not pdf_folder_path.exists():
        logger.warning(f"PDF folder {pdf_folder} not found")
        return chunks
    
    pdf_files = list(pdf_folder_path.glob("*.pdf"))
    logger.info(f"Found {len(pdf_files)} PDF files to process")
    
    for pdf_file in pdf_files:
        logger.info(f"Processing {pdf_file.name}...")
        
        # Extract text from PDF
        text = extract_text_from_pdf(str(pdf_file))
        if not text.strip():
            logger.warning(f"No text extracted from {pdf_file.name}")
            continue

        # Convert text to markdown
        markdown_text = convert_text_to_markdown(text, pdf_file.name)

        # Split markdown text into chunks
        file_chunks = text_splitter.split_text(markdown_text)
        
        # Create metadata for each chunk
        for i, chunk in enumerate(file_chunks):
            chunk_data = {
                "chunk_id": len(chunks),
                "text": chunk,
                "source": pdf_file.name,
                "chunk_index": i,
                "total_chunks": len(file_chunks)
            }
            chunks.append(chunk_data)
        
        logger.info(f"Created {len(file_chunks)} chunks from {pdf_file.name}")
    
    logger.info(f"Total chunks created: {len(chunks)}")
    return chunks

# Initialize Gemini
def initialize_gemini():
    """Initialize Google Gemini API"""
    api_key = os.getenv("GEMINI_API_KEY") or "AIzaSyC8_LPWU1jdzE_yKvyvNwaiu0-9JhNYkMg"
    if not api_key:
        logger.warning("GEMINI_API_KEY not found in environment variables")
        return False

    try:
        genai.configure(api_key=api_key)
        logger.info("Gemini API initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize Gemini API: {e}")
        return False

# FAISS and Embedding Functions
def create_embeddings_for_chunks(chunks: List[Dict[str, Any]]) -> np.ndarray:
    """Create embeddings for text chunks using Google's embedding model"""
    try:
        # For this POC, we'll use dummy embeddings
        # In production, you would use Google's text-embedding-004 model
        dimension = 384
        embeddings = np.random.random((len(chunks), dimension)).astype('float32')
        logger.info(f"Created {len(embeddings)} embeddings with dimension {dimension}")
        return embeddings
    except Exception as e:
        logger.error(f"Failed to create embeddings: {e}")
        return np.array([])

def initialize_faiss_index():
    """Initialize FAISS index with PDF knowledge base"""
    global faiss_index, text_chunks, chunk_metadata

    try:
        # Check if cached vectors exist
        cache_files = {
            "chunks": "cached_chunks.pkl",
            "embeddings": "cached_embeddings.npy",
            "faiss_index": "cached_faiss_index.bin"
        }

        # Check if all cache files exist
        cache_exists = all(os.path.exists(file) for file in cache_files.values())

        if cache_exists:
            logger.info("Loading cached vectors and FAISS index...")

            # Load cached chunks
            with open(cache_files["chunks"], "rb") as f:
                chunk_metadata = pickle.load(f)

            text_chunks = [chunk["text"] for chunk in chunk_metadata]

            # Load cached embeddings
            embeddings = np.load(cache_files["embeddings"])

            # Load cached FAISS index
            faiss_index = faiss.read_index(cache_files["faiss_index"])

            logger.info(f"Loaded cached FAISS index with {len(text_chunks)} chunks")
            return True

        else:
            logger.info("No cache found, processing PDFs and creating new vectors...")

            # Process PDFs to create chunks
            chunks = process_pdfs_to_chunks()
            if not chunks:
                logger.warning("No chunks created from PDFs, using fallback knowledge base")
                chunks = create_fallback_knowledge_base()

            text_chunks = [chunk["text"] for chunk in chunks]
            chunk_metadata = chunks

            # Create embeddings
            embeddings = create_embeddings_for_chunks(chunks)
            if embeddings.size == 0:
                logger.error("Failed to create embeddings")
                return False

            # Initialize FAISS index
            dimension = embeddings.shape[1]
            faiss_index = faiss.IndexFlatL2(dimension)
            faiss_index.add(embeddings)

            # Save everything to cache
            logger.info("Saving vectors and index to cache...")

            # Save chunks
            with open(cache_files["chunks"], "wb") as f:
                pickle.dump(chunk_metadata, f)

            # Save embeddings
            np.save(cache_files["embeddings"], embeddings)

            # Save FAISS index
            faiss.write_index(faiss_index, cache_files["faiss_index"])

            # Also save metadata for backward compatibility
            with open("chunk_metadata.pkl", "wb") as f:
                pickle.dump(chunk_metadata, f)

            logger.info(f"FAISS index initialized and cached with {len(text_chunks)} chunks")
            return True

    except Exception as e:
        logger.error(f"Failed to initialize FAISS index: {e}")
        return False

def create_fallback_knowledge_base() -> List[Dict[str, Any]]:
    """Create a fallback knowledge base if PDF processing fails"""
    fallback_texts = [
        "The Big Five personality traits, also known as the five-factor model (FFM) and the OCEAN model, is a taxonomy for personality traits. The five factors are: Openness to experience, Conscientiousness, Extraversion, Agreeableness, and Neuroticism.",
        "Agreeableness is a personality trait manifesting itself in individual behavioral characteristics that are perceived as kind, sympathetic, cooperative, warm, and considerate. People who are agreeable tend to be trusting, helpful, and compassionate.",
        "Conscientiousness is the personality trait of being careful, or diligent. Conscientiousness implies a desire to do a task well, and to take obligations to others seriously. Conscientious people tend to be efficient and organized.",
        "Extraversion is characterized by excitability, sociability, talkativeness, assertiveness, and high amounts of emotional expressiveness. People who are extraverted tend to be outgoing and energetic.",
        "Openness to experience is a general appreciation for art, emotion, adventure, unusual ideas, curiosity, and variety of experience. People who are open to experience are intellectually curious and creative.",
        "Neuroticism is a trait characterized by sadness, moodiness, and emotional instability. Individuals who are high in neuroticism tend to experience mood swings, anxiety, irritability, and sadness.",
        "Psychometric test validation involves ensuring that a test measures what it claims to measure. This includes content validity, construct validity, and criterion validity.",
        "Item analysis in psychometrics involves examining individual test items to determine their effectiveness in measuring the intended construct. This includes difficulty analysis and discrimination analysis.",
        "Holland's RIASEC model categorizes occupational interests into six types: Realistic, Investigative, Artistic, Social, Enterprising, and Conventional.",
        "Realistic personality types prefer working with tools, machines, and physical materials. They tend to be practical, mechanical, and prefer concrete rather than abstract problems."
    ]
    
    chunks = []
    for i, text in enumerate(fallback_texts):
        chunk_data = {
            "chunk_id": i,
            "text": text,
            "source": "fallback_knowledge_base",
            "chunk_index": i,
            "total_chunks": len(fallback_texts)
        }
        chunks.append(chunk_data)
    
    return chunks

def faiss_search(query: str, top_k: int = 3) -> List[str]:
    """Search FAISS index for relevant chunks"""
    global faiss_index, text_chunks
    
    if faiss_index is None or not text_chunks:
        logger.warning("FAISS index not initialized")
        return []
    
    try:
        # In production, you would embed the query using Google's embedding model
        # For this POC, we'll return random chunks
        indices = np.random.choice(len(text_chunks), min(top_k, len(text_chunks)), replace=False)
        return [text_chunks[i] for i in indices]
        
    except Exception as e:
        logger.error(f"FAISS search failed: {e}")
        return []

# Prompt Templates
GENERATOR_PROMPT_TEMPLATE = """You are an expert psychometrician designing a test item. Your task is to create a single, complete question object. You must invent a creative, relevant, and unbiased scenario for the question.

**Request:**
- Theory: {theory}
- Construct to Measure: {construct}
- Target Age: {target_age_group}

**Instructions:**
1. Create a `question_text` based on an original scenario relevant to the target age and construct.
2. Create an array of 2-4 `options`.
3. For EACH option, create a `scoring_key` object containing:
   - `"trait"`: The specific construct this option measures.
   - `"weight"`: An integer score representing the intensity of the trait (e.g., 2 for strong, 1 for mild, -1 for disagreement).
4. You MUST respond ONLY with a single, minified JSON object containing `question_text` and the `options` array.

Example format:
{{"question_text": "Your scenario question here", "options": [{{"option_id": "A", "option_text": "Option text", "scoring_key": {{"trait": "{construct}", "weight": 2}}}}]}}"""

VALIDATOR_PROMPT_TEMPLATE = """You are a meticulous Quality Assurance specialist and a PhD-level psychometrician. Your task is to validate a single psychometric question object by grounding your analysis in the provided expert knowledge from established sources.

**THEORETICAL CONTEXT (Construct Definition & Theory):**
---
{theory_chunks}
---

**VALIDATION GUIDELINES (Psychometric Standards):**
---
{validation_chunks}
---

**SIMILAR VALIDATED QUESTIONS (Comparative Examples):**
---
{similar_questions}
---

**Question Object to Validate:**
---
{question_json}
---

**Your Task:**
Based on the provided expert knowledge, evaluate the entire question object across these dimensions:
1. **Theoretical Alignment**: Does the question accurately measure the intended construct?
2. **Age Appropriateness**: Is the scenario and language suitable for the target age group?
3. **Scoring Validity**: Are the scoring weights logically consistent with trait theory?
4. **Bias Assessment**: Are there any cultural, gender, or socioeconomic biases?

You MUST respond ONLY with a single, minified JSON object with this structure:
{{"verdict": "PASS", "confidence_score": 0.92, "justification": "Brief explanation for verdict", "detailed_analysis": {{"theoretical_alignment": "STRONG", "age_appropriateness": "APPROPRIATE", "scoring_validity": "VALID", "bias_assessment": "NO_BIAS_DETECTED"}}}}"""

# Initialize services on startup
@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    logger.info("Starting Psychometric Question Generation Service...")

    # Initialize Gemini
    gemini_initialized = initialize_gemini()
    if not gemini_initialized:
        logger.warning("Gemini API not initialized - some features may not work")

    # Initialize FAISS
    faiss_initialized = initialize_faiss_index()
    if not faiss_initialized:
        logger.error("FAISS index initialization failed")

    logger.info("Service startup completed")

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Psychometric Question Generation Service",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "faiss_initialized": faiss_index is not None,
        "total_chunks": len(text_chunks)
    }

# Helper Functions
async def generate_single_question(theory: str, construct: str, target_age_group: str) -> Dict[str, Any]:
    """Generate a single question using Gemini"""
    try:
        model = genai.GenerativeModel('gemini-2.5-flash')

        prompt = GENERATOR_PROMPT_TEMPLATE.format(
            theory=theory,
            construct=construct,
            target_age_group=target_age_group
        )

        response = model.generate_content(prompt)

        if not response or not response.text:
            logger.error("Empty response from Gemini API")
            raise ValueError("Empty response from Gemini API")

        response_text = response.text.strip()
        logger.info(f"Gemini response: {response_text[:200]}...")  # Log first 200 chars

        # Try to extract JSON from the response
        try:
            # Look for JSON in the response
            if '{' in response_text and '}' in response_text:
                start_idx = response_text.find('{')
                end_idx = response_text.rfind('}') + 1
                json_str = response_text[start_idx:end_idx]
                question_data = json.loads(json_str)
            else:
                raise ValueError("No JSON found in response")
        except json.JSONDecodeError as json_error:
            logger.error(f"JSON parsing failed: {json_error}")
            logger.error(f"Response text: {response_text}")
            raise ValueError(f"Invalid JSON response: {json_error}")

        return question_data

    except Exception as e:
        logger.error(f"Failed to generate question: {e}")
        # Return a fallback question
        return {
            "question_text": f"In situations that require {construct.lower()}, how do you typically respond?",
            "options": [
                {
                    "option_id": "A",
                    "option_text": "I approach it with confidence",
                    "scoring_key": {"trait": construct, "weight": 2}
                },
                {
                    "option_id": "B",
                    "option_text": "I proceed with some hesitation",
                    "scoring_key": {"trait": construct, "weight": 1}
                }
            ]
        }

def create_question_object(question_data: Dict[str, Any], theory: str, construct: str,
                          grade: int, target_age_group: str, batch_id: str, question_num: int) -> Question:
    """Create a Question object from generated data"""

    question_id = f"q_{question_num:03d}"

    # Create options
    options = []
    for opt_data in question_data.get("options", []):
        option = Option(
            option_id=opt_data["option_id"],
            option_text=opt_data["option_text"],
            scoring_key=ScoringKey(
                trait=opt_data["scoring_key"]["trait"],
                weight=opt_data["scoring_key"]["weight"]
            )
        )
        options.append(option)

    # Create metadata
    metadata = QuestionMetadata(
        theory=theory,
        construct_name=construct,
        grade=grade,
        target_age_group=target_age_group,
        batch_id=batch_id,
        status="UNVALIDATED"
    )

    # Create question
    question = Question(
        question_id=question_id,
        question_text=question_data["question_text"],
        options=options,
        metadata=metadata
    )

    return question

@app.post("/v1/questions/generate_balanced_batch", response_model=GenerationResponse)
async def generate_balanced_batch(request: GenerationRequest):
    """Generate a balanced batch of questions across all constructs"""

    # Validate theory
    if request.theory not in THEORY_CONSTRUCT_MAP:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported theory. Supported theories: {list(THEORY_CONSTRUCT_MAP.keys())}"
        )

    # Validate grade
    if str(request.grade) not in GRADE_AGE_MAP:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported grade. Supported grades: {list(GRADE_AGE_MAP.keys())}"
        )

    try:
        # Generate batch ID
        batch_id = f"batch_{uuid.uuid4().hex[:8]}-{uuid.uuid4().hex[:4]}-{uuid.uuid4().hex[:4]}"

        # Get constructs and target age group
        constructs = THEORY_CONSTRUCT_MAP[request.theory]
        target_age_group = GRADE_AGE_MAP[str(request.grade)]

        # Calculate questions per construct
        questions_per_construct = request.generation_count // len(constructs)
        remainder = request.generation_count % len(constructs)

        # Create construct split
        construct_split = {}
        for i, construct in enumerate(constructs):
            count = questions_per_construct + (1 if i < remainder else 0)
            construct_split[construct] = count

        # Generate questions
        questions = []
        question_counter = 1

        for construct in constructs:
            questions_to_generate = construct_split[construct]

            for _ in range(questions_to_generate):
                # Generate question data
                question_data = await generate_single_question(
                    request.theory, construct, target_age_group
                )

                # Create question object
                question = create_question_object(
                    question_data, request.theory, construct,
                    request.grade, target_age_group, batch_id, question_counter
                )

                questions.append(question)
                question_counter += 1

        # Create response
        response = GenerationResponse(
            message="Balanced batch generation completed successfully.",
            batch_id=batch_id,
            total_questions_created=len(questions),
            construct_split=construct_split,
            questions=questions
        )

        logger.info(f"Generated batch {batch_id} with {len(questions)} questions")
        return response

    except Exception as e:
        logger.error(f"Batch generation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Batch generation failed: {str(e)}")

async def validate_single_question_with_rag(question: Question) -> QuestionValidationResponse:
    """Validate a single question using multi-stage RAG"""

    try:
        # Stage 1: Retrieve Construct-Specific Theory
        theory_query = f"{question.metadata.theory} {question.metadata.construct_name} definition theory psychological"
        theory_chunks = faiss_search(theory_query, top_k=3)

        # Stage 2: Retrieve Validation Guidelines
        validation_query = "psychometric test item validation guidelines scoring key assessment"
        validation_chunks = faiss_search(validation_query, top_k=2)

        # Stage 3: Retrieve Similar Validated Questions
        similar_questions = faiss_search(question.question_text, top_k=2)

        # Combine all context
        theory_context = "\n".join(theory_chunks)
        validation_context = "\n".join(validation_chunks)
        similar_context = "\n".join(similar_questions)

        # Prepare question JSON for validation
        question_json = question.model_dump_json()

        # Create validation prompt
        validation_prompt = VALIDATOR_PROMPT_TEMPLATE.format(
            theory_chunks=theory_context,
            validation_chunks=validation_context,
            similar_questions=similar_context,
            question_json=question_json
        )

        # Call Gemini for validation
        model = genai.GenerativeModel('gemini-2.5-flash')
        response = model.generate_content(validation_prompt)

        # Parse validation result
        validation_data = json.loads(response.text.strip())

        # Create validation result
        validation_result = ValidationResult(
            verdict=validation_data["verdict"],
            confidence_score=validation_data["confidence_score"],
            justification=validation_data["justification"],
            detailed_analysis=DetailedAnalysis(**validation_data["detailed_analysis"])
        )

        # Create response
        response_obj = QuestionValidationResponse(
            question_id=question.question_id,
            validation_result=validation_result,
            retrieval_info={
                "theory_chunks_found": len(theory_chunks),
                "validation_chunks_found": len(validation_chunks),
                "similar_questions_found": len(similar_questions),
                "total_context_length": len(theory_context) + len(validation_context) + len(similar_context)
            }
        )

        return response_obj

    except Exception as e:
        logger.error(f"Question validation failed: {e}")
        # Return a fallback validation result
        validation_result = ValidationResult(
            verdict="FAIL",
            confidence_score=0.0,
            justification=f"Validation failed due to error: {str(e)}",
            detailed_analysis=DetailedAnalysis(
                theoretical_alignment="UNKNOWN",
                age_appropriateness="UNKNOWN",
                scoring_validity="UNKNOWN",
                bias_assessment="UNKNOWN"
            )
        )

        return QuestionValidationResponse(
            question_id=question.question_id,
            validation_result=validation_result,
            retrieval_info={
                "theory_chunks_found": 0,
                "validation_chunks_found": 0,
                "similar_questions_found": 0,
                "total_context_length": 0
            }
        )

@app.post("/v1/questions/validate_question", response_model=QuestionValidationResponse)
async def validate_question(request: ValidationRequest):
    """Validate a single question using multi-stage FAISS-powered RAG validation"""

    try:
        result = await validate_single_question_with_rag(request.question)
        logger.info(f"Validated question {request.question.question_id}: {result.validation_result.verdict}")
        return result

    except Exception as e:
        logger.error(f"Question validation endpoint failed: {e}")
        raise HTTPException(status_code=500, detail=f"Question validation failed: {str(e)}")

@app.post("/v1/questions/validate_batch", response_model=BatchValidationResponse)
async def validate_batch(request: BatchValidationRequest):
    """Validate multiple questions at once using multi-stage RAG approach"""

    try:
        validation_results = []

        # Validate each question
        for question in request.questions:
            result = await validate_single_question_with_rag(question)
            validation_results.append(result)

        # Calculate summary statistics
        total_processed = len(validation_results)
        passed = sum(1 for r in validation_results if r.validation_result.verdict == "PASS")
        failed = total_processed - passed

        # Calculate average confidence
        confidence_scores = [r.validation_result.confidence_score for r in validation_results]
        average_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0

        # Create summary
        summary = {
            "total_processed": total_processed,
            "passed": passed,
            "failed": failed,
            "average_confidence": round(average_confidence, 2)
        }

        response = BatchValidationResponse(
            batch_validation_results=validation_results,
            summary=summary
        )

        logger.info(f"Batch validation completed: {passed}/{total_processed} passed")
        return response

    except Exception as e:
        logger.error(f"Batch validation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Batch validation failed: {str(e)}")

# Additional utility endpoints
@app.get("/v1/theories")
async def get_supported_theories():
    """Get list of supported theories and their constructs"""
    return {
        "supported_theories": THEORY_CONSTRUCT_MAP,
        "supported_grades": GRADE_AGE_MAP
    }

@app.get("/v1/theories/{theory}/constructs")
async def get_theory_constructs(theory: str):
    """Get constructs for a specific theory"""
    if theory not in THEORY_CONSTRUCT_MAP:
        raise HTTPException(
            status_code=404,
            detail=f"Theory not found. Supported theories: {list(THEORY_CONSTRUCT_MAP.keys())}"
        )

    return {
        "theory": theory,
        "constructs": THEORY_CONSTRUCT_MAP[theory]
    }

@app.post("/v1/admin/clear_cache")
async def clear_cache():
    """Clear the cached vectors and force reprocessing of PDFs"""
    try:
        cache_files = [
            "cached_chunks.pkl",
            "cached_embeddings.npy",
            "cached_faiss_index.bin",
            "chunk_metadata.pkl"
        ]

        removed_files = []
        for file in cache_files:
            if os.path.exists(file):
                os.remove(file)
                removed_files.append(file)

        return {
            "message": "Cache cleared successfully",
            "removed_files": removed_files,
            "note": "Restart the service to reprocess PDFs"
        }

    except Exception as e:
        logger.error(f"Failed to clear cache: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to clear cache: {str(e)}")

@app.get("/v1/admin/cache_status")
async def get_cache_status():
    """Get information about the current cache status"""
    cache_files = {
        "chunks": "cached_chunks.pkl",
        "embeddings": "cached_embeddings.npy",
        "faiss_index": "cached_faiss_index.bin",
        "metadata": "chunk_metadata.pkl"
    }

    status = {}
    for name, file in cache_files.items():
        if os.path.exists(file):
            stat = os.stat(file)
            status[name] = {
                "exists": True,
                "size_mb": round(stat.st_size / (1024 * 1024), 2),
                "modified": datetime.fromtimestamp(stat.st_mtime).isoformat()
            }
        else:
            status[name] = {"exists": False}

    return {
        "cache_status": status,
        "total_chunks": len(text_chunks) if text_chunks else 0,
        "faiss_initialized": faiss_index is not None
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8008)
