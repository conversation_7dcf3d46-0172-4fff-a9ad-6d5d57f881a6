#!/usr/bin/env python3
"""
Complete test script for the Psychometric Question Generation Service
Tests all endpoints and demonstrates the full functionality
"""

import json
import requests
import time
from typing import Dict, Any

# Service configuration
BASE_URL = "http://localhost:8008"

def print_section(title: str):
    """Print a formatted section header"""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print('='*60)

def test_health_and_status():
    """Test health and cache status endpoints"""
    print_section("HEALTH & STATUS CHECKS")
    
    # Health check
    print("📊 Health Check:")
    response = requests.get(f"{BASE_URL}/health")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Status: {data['status']}")
        print(f"✅ FAISS Initialized: {data['faiss_initialized']}")
        print(f"✅ Total Chunks: {data['total_chunks']}")
    else:
        print(f"❌ Health check failed: {response.status_code}")
        return False
    
    # Cache status
    print("\n💾 Cache Status:")
    response = requests.get(f"{BASE_URL}/v1/admin/cache_status")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Total Chunks: {data['total_chunks']}")
        print(f"✅ FAISS Initialized: {data['faiss_initialized']}")
        
        cache_status = data['cache_status']
        total_cache_size = 0
        for name, info in cache_status.items():
            if info['exists']:
                size_mb = info['size_mb']
                total_cache_size += size_mb
                print(f"✅ {name.title()}: {size_mb} MB")
            else:
                print(f"❌ {name.title()}: Not cached")
        
        print(f"📦 Total Cache Size: {total_cache_size:.2f} MB")
    else:
        print(f"❌ Cache status failed: {response.status_code}")
    
    return True

def test_theories():
    """Test theory endpoints"""
    print_section("SUPPORTED THEORIES")
    
    # Get all theories
    response = requests.get(f"{BASE_URL}/v1/theories")
    if response.status_code == 200:
        data = response.json()
        theories = data['supported_theories']
        grades = data['supported_grades']
        
        print("📚 Supported Theories:")
        for theory, constructs in theories.items():
            print(f"  • {theory}: {len(constructs)} constructs")
            for construct in constructs:
                print(f"    - {construct}")
        
        print(f"\n🎓 Supported Grades: {list(grades.keys())}")
        
        # Test specific theory
        theory_name = "Big Five Personality"
        response = requests.get(f"{BASE_URL}/v1/theories/{theory_name}/constructs")
        if response.status_code == 200:
            data = response.json()
            print(f"\n🔍 {theory_name} Constructs:")
            for construct in data['constructs']:
                print(f"  • {construct}")
        
        return True
    else:
        print(f"❌ Failed to get theories: {response.status_code}")
        return False

def test_question_generation():
    """Test question generation"""
    print_section("QUESTION GENERATION")
    
    request_data = {
        "generation_count": 10,
        "theory": "Big Five Personality",
        "grade": 11
    }
    
    print(f"🎯 Generating {request_data['generation_count']} questions...")
    print(f"   Theory: {request_data['theory']}")
    print(f"   Grade: {request_data['grade']}")
    
    start_time = time.time()
    response = requests.post(
        f"{BASE_URL}/v1/questions/generate_balanced_batch",
        json=request_data
    )
    end_time = time.time()
    
    if response.status_code == 200:
        data = response.json()
        
        print(f"✅ Generation completed in {end_time - start_time:.2f} seconds")
        print(f"📊 Batch ID: {data['batch_id']}")
        print(f"📊 Total Questions: {data['total_questions_created']}")
        print(f"📊 Construct Distribution:")
        
        for construct, count in data['construct_split'].items():
            print(f"   • {construct}: {count} questions")
        
        # Show first question as example
        if data['questions']:
            first_q = data['questions'][0]
            print(f"\n📝 Example Question (ID: {first_q['question_id']}):")
            print(f"   Text: {first_q['question_text'][:100]}...")
            print(f"   Options: {len(first_q['options'])}")
            print(f"   Construct: {first_q['metadata']['construct_name']}")
            print(f"   Target Age: {first_q['metadata']['target_age_group']}")
        
        return data
    else:
        print(f"❌ Generation failed: {response.status_code}")
        try:
            error_data = response.json()
            print(f"   Error: {error_data.get('detail', 'Unknown error')}")
        except:
            print(f"   Raw error: {response.text}")
        return None

def test_question_validation(questions_data: Dict[str, Any]):
    """Test question validation"""
    print_section("QUESTION VALIDATION")
    
    if not questions_data or not questions_data.get('questions'):
        print("❌ No questions available for validation")
        return False
    
    # Test single question validation
    first_question = questions_data['questions'][0]
    
    print(f"🔍 Validating question: {first_question['question_id']}")
    
    request_data = {"question": first_question}
    
    start_time = time.time()
    response = requests.post(
        f"{BASE_URL}/v1/questions/validate_question",
        json=request_data
    )
    end_time = time.time()
    
    if response.status_code == 200:
        data = response.json()
        result = data['validation_result']
        
        print(f"✅ Validation completed in {end_time - start_time:.2f} seconds")
        print(f"📊 Question ID: {data['question_id']}")
        print(f"📊 Verdict: {result['verdict']}")
        print(f"📊 Confidence: {result['confidence_score']:.2f}")
        print(f"📊 Justification: {result['justification']}")
        
        analysis = result['detailed_analysis']
        print(f"📊 Detailed Analysis:")
        print(f"   • Theoretical Alignment: {analysis['theoretical_alignment']}")
        print(f"   • Age Appropriateness: {analysis['age_appropriateness']}")
        print(f"   • Scoring Validity: {analysis['scoring_validity']}")
        print(f"   • Bias Assessment: {analysis['bias_assessment']}")
        
        retrieval = data['retrieval_info']
        print(f"📊 Retrieval Info:")
        print(f"   • Theory chunks: {retrieval['theory_chunks_found']}")
        print(f"   • Validation chunks: {retrieval['validation_chunks_found']}")
        print(f"   • Similar questions: {retrieval['similar_questions_found']}")
        print(f"   • Total context length: {retrieval['total_context_length']}")
        
        return True
    else:
        print(f"❌ Validation failed: {response.status_code}")
        return False

def test_batch_validation(questions_data: Dict[str, Any]):
    """Test batch validation"""
    print_section("BATCH VALIDATION")
    
    if not questions_data or not questions_data.get('questions'):
        print("❌ No questions available for batch validation")
        return False
    
    # Use first 3 questions for batch validation
    questions_to_validate = questions_data['questions'][:3]
    
    print(f"🔍 Validating batch of {len(questions_to_validate)} questions...")
    
    request_data = {"questions": questions_to_validate}
    
    start_time = time.time()
    response = requests.post(
        f"{BASE_URL}/v1/questions/validate_batch",
        json=request_data
    )
    end_time = time.time()
    
    if response.status_code == 200:
        data = response.json()
        summary = data['summary']
        
        print(f"✅ Batch validation completed in {end_time - start_time:.2f} seconds")
        print(f"📊 Summary:")
        print(f"   • Total Processed: {summary['total_processed']}")
        print(f"   • Passed: {summary['passed']}")
        print(f"   • Failed: {summary['failed']}")
        print(f"   • Average Confidence: {summary['average_confidence']}")
        
        print(f"📊 Individual Results:")
        for result in data['batch_validation_results']:
            verdict = result['validation_result']['verdict']
            confidence = result['validation_result']['confidence_score']
            print(f"   • {result['question_id']}: {verdict} (confidence: {confidence:.2f})")
        
        return True
    else:
        print(f"❌ Batch validation failed: {response.status_code}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Complete Service Test")
    print(f"🌐 Testing service at: {BASE_URL}")
    
    # Test 1: Health and Status
    if not test_health_and_status():
        print("❌ Health check failed. Is the service running?")
        return
    
    # Test 2: Theories
    if not test_theories():
        print("❌ Theory endpoints failed")
        return
    
    # Test 3: Question Generation
    questions_data = test_question_generation()
    if not questions_data:
        print("❌ Question generation failed")
        return
    
    # Test 4: Single Question Validation
    if not test_question_validation(questions_data):
        print("❌ Question validation failed")
        return
    
    # Test 5: Batch Validation
    if not test_batch_validation(questions_data):
        print("❌ Batch validation failed")
        return
    
    # Final Summary
    print_section("TEST SUMMARY")
    print("🎉 All tests completed successfully!")
    print("✅ Service is fully functional")
    print("✅ PDF processing and caching working")
    print("✅ Question generation working")
    print("✅ Question validation working")
    print("✅ FAISS vector search working")
    print("✅ Gemini API integration working")

if __name__ == "__main__":
    main()
